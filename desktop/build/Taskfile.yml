version: "3"

vars:
  APP_NAME: ns-drive
  FRONTEND_DIR: frontend
  DIST_DIR: frontend/dist/browser
  BIN_DIR: bin

tasks:
  # Install frontend dependencies
  install:frontend:deps:
    summary: Install frontend dependencies
    dir: "{{.FRONTEND_DIR}}"
    cmds:
      - yarn install

  # Build frontend for production
  build:frontend:
    summary: Build frontend for production
    dir: "{{.FRONTEND_DIR}}"
    cmds:
      - yarn build
    sources:
      - src/**/*
      - package.json
      - angular.json
      - tsconfig.json
    generates:
      - dist/browser/**/*

  # Build frontend for development with watch
  dev:frontend:
    summary: Build frontend for development with watch
    dir: "{{.FRONTEND_DIR}}"
    cmds:
      - yarn start

  # Generate bindings
  generate:bindings:
    summary: Generate TypeScript bindings from Go code
    cmds:
      - wails3 generate bindings

  # Clean build artifacts
  clean:
    summary: Clean build artifacts
    cmds:
      - rm -rf {{.BIN_DIR}}/
      - rm -rf {{.DIST_DIR}}

  # Format code
  fmt:
    summary: Format Go code
    cmds:
      - go fmt ./...

  # Run tests
  test:
    summary: Run tests
    cmds:
      - go test ./...
